using System.IO;
using System.Threading.Tasks;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Logging;

namespace DriverLicenseProcessor;

public class ProcessDriverLicense
{
    private readonly ILogger<ProcessDriverLicense> _logger;

    public ProcessDriverLicense(ILogger<ProcessDriverLicense> logger)
    {
        _logger = logger;
    }

    [Function(nameof(ProcessDriverLicense))]
    public async Task Run([BlobTrigger("driver-license-uploads/{name}", Connection = "StorageConnection")] Stream stream, string name)
    {
        using var blobStreamReader = new StreamReader(stream);
        var content = await blobStreamReader.ReadToEndAsync();
        _logger.LogInformation("C# Blob trigger function Processed blob\n Name: {name} \n Data: {content}", name, content);
    }
}