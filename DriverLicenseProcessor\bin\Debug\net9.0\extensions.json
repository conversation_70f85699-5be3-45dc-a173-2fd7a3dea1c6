{"extensions": [{"name": "AzureStorageBlobs", "typeName": "Microsoft.Azure.WebJobs.Extensions.Storage.AzureStorageBlobsWebJobsStartup, Microsoft.Azure.WebJobs.Extensions.Storage.Blobs, Version=5.3.4.0, Culture=neutral, PublicKeyToken=92742159e12e44c8", "hintPath": "./.azurefunctions/Microsoft.Azure.WebJobs.Extensions.Storage.Blobs.dll"}, {"name": "Startup", "typeName": "Microsoft.Azure.WebJobs.Extensions.FunctionMetadataLoader.Startup, Microsoft.Azure.WebJobs.Extensions.FunctionMetadataLoader, Version=1.0.0.0, Culture=neutral, PublicKeyToken=551316b6919f366c", "hintPath": "./.azurefunctions/Microsoft.Azure.WebJobs.Extensions.FunctionMetadataLoader.dll"}]}