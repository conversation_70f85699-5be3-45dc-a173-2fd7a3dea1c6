{"dependencies": {"appInsights1": {"type": "appInsights.sdk"}, "secrets1": {"type": "secrets.user"}, "storage1": {"serviceConnectorResourceId": "/subscriptions/[parameters('subscriptionId')]/resourceGroups/[parameters('serviceConnectorResourceGroupName')]/providers/Microsoft.ServiceLinker/locations/southeastasia/connectors/StorageConnection_D24CFEF2FE, /subscriptions/1deedf2a-89e9-448d-8bd9-eca56addbc33/resourceGroups/rg-serverless-course/providers/Microsoft.ServiceLinker/locations/southeastasia/connectors/StorageConnection_60E4C25DD9", "secretStore": "LocalSecretsFile", "resourceId": "/subscriptions/[parameters('subscriptionId')]/resourceGroups/[parameters('resourceGroupName')]/providers/Microsoft.Storage/storageAccounts/stgserverlesscourse", "type": "storage.azure", "connectionId": "StorageConnection", "dynamicId": null}}}